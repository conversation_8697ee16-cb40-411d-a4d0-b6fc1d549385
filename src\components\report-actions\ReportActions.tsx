import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Button } from '@/components';
import { primaryColor } from '@/constants';
import { IReport } from '@/models';

interface ReportActionsProps {
	data: IReport | null;
	canPrintProducts: boolean;
	isLoadingReport: boolean;
	onReport: () => void;
	onPrint: () => void;
	onPrintProducts: () => void;
}

export const ReportActions: React.FC<ReportActionsProps> = ({
	data,
	canPrintProducts,
	isLoadingReport,
	onReport,
	onPrint,
	onPrintProducts,
}) => {
	return (
		<View style={styles.container}>
			<Button
				onPress={onReport}
				text={isLoadingReport ? "Đang tải..." : "Thống kê"}
				style={styles.btn}
				textStyle={styles.btnLabel}
				disabled={isLoadingReport}
			/>
			{data && (
				<>
					<Button
						onPress={onPrint}
						text="In kết ca"
						style={styles.btn}
						textStyle={styles.btnLabel}
						background={
							typeof data.detail === 'undefined' ? '#ddd' : primaryColor
						}
						disabled={typeof data.detail === 'undefined'}
					/>
					<Button
						onPress={onPrintProducts}
						text="In hàng hóa"
						style={styles.btn}
						textStyle={styles.btnLabel}
						background={!canPrintProducts ? '#ddd' : primaryColor}
						disabled={!canPrintProducts}
					/>
				</>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		marginTop: 20,
		gap: 10,
	},
	btn: {
		paddingVertical: 12,
		paddingHorizontal: 20,
		borderRadius: 6,
	},
	btnLabel: {
		fontSize: 16,
		fontWeight: '600',
	},
});
