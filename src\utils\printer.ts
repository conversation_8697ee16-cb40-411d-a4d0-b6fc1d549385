import { IBill, ITicket } from '@/modules/pos';
import { currencyFormat, getDateTime } from './common';

export const transformBill = (data: any) => {
	const order: IBill = {
		detail: {
			counter: data.counter,
			seller: data.seller,
			time: data.time,
			hd: data.bill,
			order_id: data.order_id,
			member: data.member,
		},
		products: data.products.map((item: any) => {
			return [
				item.name,
				item.quantity,
				currencyFormat(item.price),
				currencyFormat(item.total_amount),
			];
		}),
		payment: {
			total: currencyFormat(data.amount.total) + ' đ',
			input: currencyFormat(data.amount.input) + ' đ',
			refund: currencyFormat(data.amount.refund) + ' đ',
		},
	};
	return order;
};

export const transformTickets = (data: any) => {
	const tickets: ITicket[] = [];
	for (const i in data.tickets) {
		const item = data.tickets[i];

		const ticket_id = item.ticket_id || item.ticket || item.id;

		const count: Record<string, number> = {};

		data.tickets.forEach((seat: any) => {
			let target = seat.target || 'Người lớn';
			let sortTarget = target
				.replace('Người lớn Member', 'NL')
				.replace('Người lớn', 'NL')
				.replace('HSSV Member', 'HS')
				.replace('HSSV', 'HS')
				.replace('Trẻ em, NCT, DTUT Member', 'TE')
				.replace('Trẻ em, NCT, DTUT', 'TE')
				.replace('Khách mời', 'MOI');

			if (typeof count[sortTarget] !== 'undefined') {
				count[sortTarget]++;
			} else {
				count[sortTarget] = 1;
			}
		});

		const countText: string = Object.entries(count)
			.reduce((str, [p, val]) => {
				return `${str}-${val}${p}`;
			}, '')
			.replace(/^-/i, '');
		const ticket: ITicket = {
			ticket_id,
			order_id: item.order_id || item.ticket.replace('VEMOI', 'MOI') || 'VM',
			date: item.date,
			time: item.time,
			room: item.room_name,
			seat: item.seat_name,
			type: item.target || 'Vé mời',
			price: currencyFormat(item.price || 0),
			times: item.times > 0 ? 'IL: ' + item.times : '',
			movie: item.movie_name_vn + ' (' + item.movie_age + ')',
			create_date: item.create_date || getDateTime(),
			seller: data.seller,
			stt: parseInt(i) + 1,
			count: countText,
		};
		tickets.push(ticket);
	}
	return tickets;
};
