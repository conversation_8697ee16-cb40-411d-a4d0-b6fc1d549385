import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { IReport, IReportShift } from '@/models';
import { getDate } from '@/utils';

// Money denomination interface
export interface IMoneyDenomination {
	name: number;
	quantity: number;
	total: number;
}

// Report state interface
interface ReportState {
	// Money counting state
	moneyData: IMoneyDenomination[];
	moneyTotal: number;
	
	// Report state
	shifts: IReportShift[];
	from: string;
	to: string;
	shift: number;
	reportData: IReport | null;
	isLoadingShifts: boolean;
	isLoadingReport: boolean;
	error: string | null;
}

// Default money denominations
const defaultMoneyData: IMoneyDenomination[] = [
	{ name: 500000, quantity: 0, total: 0 },
	{ name: 200000, quantity: 0, total: 0 },
	{ name: 100000, quantity: 0, total: 0 },
	{ name: 50000, quantity: 0, total: 0 },
	{ name: 20000, quantity: 0, total: 0 },
	{ name: 10000, quantity: 0, total: 0 },
	{ name: 5000, quantity: 0, total: 0 },
	{ name: 2000, quantity: 0, total: 0 },
	{ name: 1000, quantity: 0, total: 0 },
	{ name: 500, quantity: 0, total: 0 },
];

const initialState: ReportState = {
	// Money state
	moneyData: defaultMoneyData,
	moneyTotal: 0,
	
	// Report state
	shifts: [],
	from: getDate() + ' 06:00',
	to: getDate() + ' 23:59',
	shift: 3,
	reportData: null,
	isLoadingShifts: false,
	isLoadingReport: false,
	error: null,
};

export const ReportSlice = createSlice({
	name: 'report',
	initialState,
	reducers: {
		// Money actions
		updateMoneyQuantity: (state, action: PayloadAction<{ index: number; quantity: number }>) => {
			const { index, quantity } = action.payload;
			if (index >= 0 && index < state.moneyData.length) {
				state.moneyData[index].quantity = quantity;
				state.moneyData[index].total = quantity > 0 ? quantity * state.moneyData[index].name : 0;
				
				// Recalculate total
				state.moneyTotal = state.moneyData.reduce((total, item) => total + item.total, 0);
			}
		},
		
		resetMoneyData: (state) => {
			state.moneyData = defaultMoneyData;
			state.moneyTotal = 0;
		},
		
		// Report actions
		setShifts: (state, action: PayloadAction<IReportShift[]>) => {
			state.shifts = action.payload;
		},
		
		setFromDate: (state, action: PayloadAction<string>) => {
			state.from = action.payload;
		},
		
		setToDate: (state, action: PayloadAction<string>) => {
			state.to = action.payload;
		},
		
		setShift: (state, action: PayloadAction<number>) => {
			state.shift = action.payload;
		},
		
		setReportData: (state, action: PayloadAction<IReport>) => {
			state.reportData = action.payload;
		},
		
		clearReportData: (state) => {
			state.reportData = null;
		},
		
		setLoadingShifts: (state, action: PayloadAction<boolean>) => {
			state.isLoadingShifts = action.payload;
		},
		
		setLoadingReport: (state, action: PayloadAction<boolean>) => {
			state.isLoadingReport = action.payload;
		},
		
		setError: (state, action: PayloadAction<string | null>) => {
			state.error = action.payload;
		},
		
		clearError: (state) => {
			state.error = null;
		},
	},
});

export const {
	updateMoneyQuantity,
	resetMoneyData,
	setShifts,
	setFromDate,
	setToDate,
	setShift,
	setReportData,
	clearReportData,
	setLoadingShifts,
	setLoadingReport,
	setError,
	clearError,
} = ReportSlice.actions;

export default ReportSlice.reducer;
