import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { useSelector } from 'react-redux';
import Icon from '@expo/vector-icons/Ionicons';
import { useThrottledCallback } from 'use-debounce';
import { router, useLocalSearchParams } from 'expo-router';

import {
	Button,
	MemberButton,
	VoucherButton,
	Separator,
	MemberCard,
} from '@/components';
import {
	useAppDispatch,
	getMoney,
	resetCart,
	getPayment,
	clearOrder,
	getMember,
	payWithQrCode,
	getConfigs,
	getUser,
	setLoading,
} from '@/store';
import { cartService, sendToCustomer } from '@/services';
import { openCashDrawer, printTask } from '@/modules/pos';
import {
	showMessage,
	isCustomer,
	transformBill,
	transformTickets,
} from '@/utils';
import { useShortcut } from '@/hooks';
import { primaryColor } from '@/constants';
import { Tab<PERSON>out<PERSON>, CreateOrderResponse, ICartAmount } from '@/models';

import Qrcode from './Qrcode';
import PaymentMethods from './PaymentMethods';

type Props = {
	orderId: number;
	amount: ICartAmount;
};

export const Sidebar = React.memo(({ orderId, amount }: Props) => {
	const dispatch = useAppDispatch();

	const params = useLocalSearchParams<{ previous_screen: string }>();

	const configs = useSelector(getConfigs);
	const user = useSelector(getUser);
	const money = useSelector(getMoney);
	const payment = useSelector(getPayment);
	const member = useSelector(getMember);

	const isCustomerScreen = isCustomer();

	const handleCheckout = async () => {
		const payViaQr = ['vnpay', 'shopeepay', 'momo'];
		if (payViaQr.includes(payment)) {
			dispatch(
				payWithQrCode({
					orderId,
					amount: amount.total,
					gateway: payment,
				}),
			);
		} else {
			doCheckout();
		}
	};

	const doCheckout = async () => {
		dispatch(setLoading(true));
		try {
			const response = await cartService.checkout({
				order_id: orderId,
				payment: 2,
				payment_method: payment,
			});
			await printOrder(response);
		} catch (e) {
			console.log(e);
		}
		dispatch(setLoading(false));
	};

	const onCheckout = useThrottledCallback(handleCheckout, 2000, {
		trailing: false,
	});

	// Register F2 shortcut for checkout
	useShortcut('F2', onCheckout, !isCustomerScreen);

	const printOrder = async (order: CreateOrderResponse) => {
		if (order.products && order.products.length > 0) {
			const refund = money > amount.total ? money - amount.total : 0;
			await printTask(
				transformBill({
					counter: configs.counter,
					seller: user.name,
					products: order.products,
					bill: order.bill,
					time: order.time,
					order_id: orderId,
					member: member ? 'KH: ' + member.member_id : '',
					amount: {
						total: amount.total,
						ticket: amount.ticket,
						input: money,
						refund: refund,
					},
				}),
				configs.printer,
				'bill',
			);
		}
		if (order.tickets && order.tickets.length > 0) {
			printTask(
				transformTickets({
					tickets: order.tickets,
					seller: user.name,
				}),
				configs.printer,
				'ticket',
			);
		}
		openCashDrawer();
		complete();
	};

	const complete = () => {
		showMessage({
			message: 'Thanh toán thành công',
			type: 'success',
		});
		sendToCustomer({
			action: 'showSuccess',
		});
		dispatch(clearOrder());
		goBack();
	};

	const cancelRequest = async () => {
		dispatch(setLoading(true));
		try {
			await cartService.cancelOrder(orderId);
		} catch (e) {
			console.log(e);
		}
		dispatch(clearOrder());
		dispatch(setLoading(false));
		goBack();
	};

	const onGoBack = async () => {
		dispatch(setLoading(true));
		try {
			await cartService.cancelOrder(orderId);
		} catch (e) {
			console.log(e);
		}
		dispatch(resetCart());
		dispatch(setLoading(false));
		goBack(true);
	};

	const goBack = (previous: boolean = false) => {
		if (params.previous_screen) {
			let toRoute = params.previous_screen as TabRoute;
			if (!previous) {
				toRoute = params.previous_screen.replace(
					'/cashier/booking',
					'/cashier/tickets',
				) as TabRoute;
			}
			router.dismissTo({
				pathname: toRoute,
			});
		} else {
			router.dismissTo('/cashier');
		}
	};

	return (
		<View style={styles.bg}>
			<Text style={styles.orderId}>
				Giao dịch #<Text style={styles.bold}>{orderId}</Text>
			</Text>
			<Separator />
			<PaymentMethods />
			{!isCustomerScreen && (
				<View style={styles.action}>
					<View style={styles.row}>
						<MemberButton style={styles.btn} textStyle={styles.btnLabel} />
						<VoucherButton
							orderId={orderId}
							style={styles.btn}
							textStyle={styles.btnLabel}
						/>
					</View>
					<Button onPress={onCheckout} style={styles.checkout}>
						<Icon name="print" size={25} color="#fff" />
						<Text style={styles.checkoutText}>THANH TOÁN (F2)</Text>
					</Button>
					<Button
						onPress={cancelRequest}
						style={styles.checkout}
						background="#f13f3f">
						<Icon name="trash" size={25} color="#fff" />
						<Text style={styles.checkoutText}>HUỶ GD</Text>
					</Button>
					<Button
						onPress={onGoBack}
						style={styles.checkout}
						background="#6cc1f1">
						<Icon name="return-up-back-outline" size={25} color="#fff" />
						<Text style={[styles.checkoutText, styles.backText]}>QUAY LẠI</Text>
					</Button>
				</View>
			)}
			<View style={styles.member}>
				<MemberCard />
			</View>
			<Qrcode
				orderId={orderId}
				amount={amount.total}
				onSuccess={doCheckout}
				payment={payment}
			/>
		</View>
	);
});

Sidebar.displayName = 'CheckoutSidebar';

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		backgroundColor: '#fff',
		borderTopLeftRadius: 10,
		padding: 10,
	},
	orderId: {
		color: primaryColor,
		fontSize: 18,
		paddingBottom: 5,
	},
	action: {
		marginVertical: 5,
	},
	checkout: {
		padding: 10,
		marginVertical: 5,
		marginHorizontal: 5,
		alignContent: 'center',
		justifyContent: 'center',
		flexDirection: 'row',
	},
	checkoutText: {
		fontSize: 20,
		textAlign: 'center',
		color: '#fff',
		paddingLeft: 10,
	},
	btnLabel: {
		fontWeight: 'bold',
		fontSize: 15,
	},
	backText: {},
	row: {
		flexDirection: 'row',
		paddingVertical: 10,
	},
	btn: {
		backgroundColor: '#ffa400',
		borderColor: '#e7822a',
		borderWidth: 2,
		flex: 1,
		alignItems: 'center',
		paddingHorizontal: 0,
		paddingVertical: 0,
		marginHorizontal: 5,
		height: 45,
		justifyContent: 'center',
	},
	member: {
		flex: 1,
		alignItems: 'center',
		justifyContent: 'flex-end',
	},
	bold: {
		fontWeight: 'bold',
	},
});
