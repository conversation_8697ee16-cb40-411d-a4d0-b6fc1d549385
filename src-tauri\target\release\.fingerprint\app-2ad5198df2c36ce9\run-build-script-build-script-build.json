{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1322478694103194923, "build_script_build", false, 6869645715303235554]], "local": [{"RerunIfChanged": {"output": "release\\build\\app-2ad5198df2c36ce9\\output", "paths": ["tauri.conf.json", "logo-ticket.png", "plugins\\cashdrawer\\cashdrawer.exe", "plugins\\cashdrawer\\cc3280.dll", "plugins\\cashdrawer\\inpout32.dll", "plugins\\TwPrinter.exe"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}