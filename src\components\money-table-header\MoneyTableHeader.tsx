import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

export const MoneyTableHeader: React.FC = () => {
	return (
		<View style={styles.row}>
			<View style={styles.label}>
				<Text style={styles.text}>Mệnh giá</Text>
			</View>
			<View style={styles.quantity}>
				<Text style={styles.text}>Số lượng</Text>
			</View>
			<View style={styles.total}>
				<Text style={styles.text}>Số tiền</Text>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	row: {
		flexDirection: 'row',
		alignItems: 'center',
		paddingVertical: 10,
		borderBottomWidth: 1,
		borderBottomColor: '#ddd',
		backgroundColor: '#f5f5f5',
	},
	label: {
		flex: 1,
		paddingHorizontal: 10,
	},
	quantity: {
		flex: 1,
		paddingHorizontal: 10,
	},
	total: {
		flex: 1,
		paddingHorizontal: 10,
	},
	text: {
		fontSize: 16,
		fontWeight: 'bold',
		color: '#333',
	},
});
