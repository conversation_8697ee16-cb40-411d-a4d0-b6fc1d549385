import React, { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';

import { currencyFormat } from '@/utils';
import { ReportInputForm, ReportActions, Separator } from '@/components';
import {
	getConfigs,
	getUser,
	getShifts,
	getFromDate,
	getToDate,
	getSelectedShift,
	getReportData,
	getCanPrintProducts,
	getIsLoadingReport,
	setFromDate,
	setToDate,
	setShift,
	fetchShifts,
	generateReport,
	useAppDispatch,
} from '@/store';
import { primaryColor } from '@/constants';
import {
	printTask,
	IReport as IPrintReport,
	IProduct as IPrintProduct,
} from '@/modules/pos';

import { Detail, Results } from './components';

export const ReportScreen = () => {
	const dispatch = useAppDispatch();

	// Redux selectors
	const user = useSelector(getUser);
	const configs = useSelector(getConfigs);
	const shifts = useSelector(getShifts);
	const from = useSelector(getFromDate);
	const to = useSelector(getToDate);
	const shift = useSelector(getSelectedShift);
	const data = useSelector(getReportData);
	const canPrintProducts = useSelector(getCanPrintProducts);
	const isLoadingReport = useSelector(getIsLoadingReport);

	useEffect(() => {
		dispatch(fetchShifts());
	}, [dispatch]);

	const onReport = async () => {
		dispatch(generateReport({ from, to, shift }));
	};

	const onPrint = () => {
		if (data) {
			const report: IPrintReport = {
				time: data.detail.start + ' - ' + data.detail.end,
				counter: configs.counter,
				seller: data.detail.user,
				shift: data.detail.shift,
				total: currencyFormat(data.detail.amount.amount_total) + ' VNĐ',
				sell: currencyFormat(data.detail.amount.amount_sell) + ' VNĐ',
				cancel: currencyFormat(data.detail.amount.amount_total_cancel) + ' VNĐ',
				cancel_count: data.tickets.canceled ? data.tickets.canceled.length : 0,
				ticket: currencyFormat(data.detail.amount.amount_ticket) + ' VNĐ',
				ticket_count: data.tickets.success ? data.tickets.success.length : 0,
				combo: currencyFormat(data.detail.amount.amount_combo) + ' VNĐ',
				combo_count: data.products.success ? data.products.success.length : 0,
				cash: currencyFormat(data.detail.amount.amount_cash) + ' VNĐ',
				card: currencyFormat(data.detail.amount.card) + ' VNĐ',
				momo: currencyFormat(data.detail.amount.momo) + ' VNĐ',
				vnpay: currencyFormat(data.detail.amount.vnpay) + ' VNĐ',
				shopeepay: currencyFormat(data.detail.amount.shopeepay || 0) + ' VNĐ',
				grab: currencyFormat(data.detail.amount.grab || 0) + ' VNĐ',
				beamin: currencyFormat(data.detail.amount.beamin || 0) + ' VNĐ',
				voucher: currencyFormat(data.detail.amount.amount_voucher) + ' VNĐ',
				voucher_count: data.detail.amount.voucher_count,
			};
			printTask(report, configs.printer, 'report');
		}
	};

	const onPrintProducts = () => {
		if (data) {
			const products: IPrintProduct = {
				time: data.detail.start + ' - ' + data.detail.end,
				counter: configs.counter,
				seller: data.detail.user,
				shift: data.detail.shift,
				products: data.categories.map((item) => {
					return [item.name, item.total];
				}),
				product_total: data.total_categories,
			};
			printTask(products, configs.printer, 'product');
		}
	};

	return (
		<View style={styles.bg}>
			<View style={styles.colDetail}>
				<View style={styles.detail}>
					<ReportInputForm
						from={from}
						to={to}
						shift={shift}
						shifts={shifts}
						userName={user.name}
						onFromChange={(value) => dispatch(setFromDate(value))}
						onToChange={(value) => dispatch(setToDate(value))}
						onShiftChange={(value) => dispatch(setShift(value))}
					/>
					<Separator />
					{data && (
						<Detail
							detail={data.detail}
							total={{
								tickets: data.tickets?.success
									? data.tickets?.success.length
									: 0,
								products: data.products?.success
									? data.products?.success.length
									: 0,
							}}
						/>
					)}
				</View>
				<View style={styles.action}>
					<Button
						onPress={onReport}
						text="Thống kê"
						style={styles.btn}
						textStyle={styles.btnLabel}
						disabled={isLoadingReport}
					/>
					{data && (
						<>
							<Button
								onPress={onPrint}
								text="In kết ca"
								style={styles.btn}
								textStyle={styles.btnLabel}
								background={
									typeof data.detail === 'undefined' ? '#ddd' : primaryColor
								}
								disabled={typeof data.detail === 'undefined'}
							/>
							<Button
								onPress={onPrintProducts}
								text="In hàng hóa"
								style={styles.btn}
								textStyle={styles.btnLabel}
								background={!canPrintProducts ? '#ddd' : primaryColor}
								disabled={!canPrintProducts}
							/>
						</>
					)}
				</View>
			</View>
			{data && (
				<Results
					overview={data.overview}
					categories={data.categories}
					total={data.total_categories || 0}
				/>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	bg: {
		flex: 1,
		flexDirection: 'row',
		paddingHorizontal: 20,
		paddingVertical: 10,
	},
	colDetail: {
		width: 350,
		backgroundColor: '#fff',
		padding: 10,
	},
	detail: {
		flex: 1,
	},
	action: {},
	row: {
		flexDirection: 'row',
		marginVertical: 5,
		alignItems: 'center',
	},
	label: {
		width: 80,
	},
	value: {
		flex: 1,
	},
	picker: {},
	btn: {
		marginVertical: 5,
	},
	btnLabel: {
		textAlign: 'center',
		color: '#fff',
	},
	input: {},
});
