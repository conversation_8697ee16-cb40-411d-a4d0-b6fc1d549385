{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 13390825267576250845, "path": 2948324163062073669, "deps": [[533142347765177280, "anyhow", false, 7843119605758917334], [3851720982022213547, "semver", false, 3436642832763427366], [4450062412064442726, "dirs_next", false, 5712351750848053873], [4570439348892781834, "serde", false, 8513030281191684935], [7468248713591957673, "cargo_toml", false, 13859846205203627619], [8292277814562636972, "tauri_utils", false, 17990544204581357387], [10301936376833819828, "json_patch", false, 5459429902284252947], [13077543566650298139, "heck", false, 3995097094540032690], [14189313126492979171, "tauri_winres", false, 9886035857711061464], [15622660310229662834, "walkdir", false, 14082555205449305640], [18425240504718578388, "serde_json", false, 13215291096198645576]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-eb3c9e945df1d809\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}