import { StyleSheet, View, Text } from 'react-native';
import { useSelector } from 'react-redux';
import Icon from '@expo/vector-icons/Ionicons';
import { router, usePathname } from 'expo-router';

import { getUser } from '@/store';
import { primaryColor } from '@/constants';
import { Clock } from '@/components';

import MenuItem from './MenuItem';
import LogoutButton from './LogoutButton';
import ExitButton from './ExitButton';
import ReloadButton from './ReloadButton';
import FullscreenButton from './FullscreenButton';
import ManagerButton from './ManagerButton';

type MenuPath =
	| '/cashier'
	| '/cashier/tickets'
	| '/cashier/products'
	| '/cashier/print'
	| '/cashier/members'
	| '/cashier/timelines'
	| '/cashier/reports'
	| '/cashier/money'
	| '/cashier/settings';

type Item = {
	icon: keyof typeof Icon.glyphMap;
	label: string;
	to: MenuPath;
	permission?: string;
};
const menus: Item[] = [
	{
		icon: 'home',
		label: 'Trang chủ',
		to: '/cashier',
	},
	{
		icon: 'images-outline',
		label: 'Bán vé',
		to: '/cashier/tickets',
		permission: 'can_ticket',
	},
	{
		icon: 'fast-food-outline',
		label: 'Bắp nước',
		to: '/cashier/products',
		permission: 'can_fastfood',
	},
	{
		icon: 'print-outline',
		label: 'In vé',
		to: '/cashier/print',
	},
	{
		icon: 'people-outline',
		label: 'Thành viên',
		to: '/cashier/members',
		permission: 'can_member',
	},
	{
		icon: 'calendar-outline',
		label: 'Lịch chiếu',
		to: '/cashier/timelines',
	},
	{
		icon: 'newspaper-outline',
		label: 'Kết ca',
		to: '/cashier/reports',
	},
	{
		icon: 'cash-outline',
		label: 'Kê tiền',
		to: '/cashier/money',
	},
	{
		icon: 'settings-outline',
		label: 'Cài đặt',
		to: '/cashier/settings',
		permission: 'setting',
	},
];

const hidden = [
	'/cashier/booking',
	'/cashier/checkout',
	'/cashier/exchange',
	'/cashier/booking-products',
];

export const Menu = () => {
	const user = useSelector(getUser);
	const pathname = usePathname();

	if (hidden.includes(pathname)) {
		return null;
	}

	return (
		<View style={styles.wrapper}>
			<View style={styles.header}>
				<View style={styles.logo}>
					<Text numberOfLines={1} style={styles.username}>
						{user.name}
					</Text>
					<Clock />
				</View>
			</View>
			<View style={styles.menu}>
				{menus.map((item) => {
					if (
						!item.permission ||
						user.permission.indexOf(item.permission) >= 0
					) {
						const onPress = () => {
							router.navigate(item.to);
						};

						return (
							<MenuItem
								iconName={item.icon}
								onPress={onPress}
								label={item.label}
								key={item.icon}
								isFocused={item.to === pathname}
							/>
						);
					}
					return null;
				})}
				<ManagerButton />
				<LogoutButton />
			</View>
			<View style={styles.footer}>
				<View>
					<ExitButton />
					<ReloadButton />
					<FullscreenButton />
				</View>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	wrapper: {
		backgroundColor: primaryColor,
		minWidth: 0,
		boxShadow: '1px 0 5px rgba(0, 0, 0, .5)',
	},
	header: {},
	menu: {},
	logo: {
		backgroundColor: '#ca1864',
		alignItems: 'center',
		justifyContent: 'center',
		paddingVertical: 5,
	},
	username: {
		color: '#eee',
		fontSize: 12,
		paddingVertical: 2,
	},
	footer: {
		justifyContent: 'flex-end',
		flex: 1,
	},
});
