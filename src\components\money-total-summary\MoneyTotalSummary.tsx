import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { currencyFormat } from '@/utils';

interface MoneyTotalSummaryProps {
	total: number;
	totalQuantity: number;
}

export const MoneyTotalSummary: React.FC<MoneyTotalSummaryProps> = ({
	total,
	totalQuantity,
}) => {
	return (
		<View style={styles.container}>
			<View style={styles.row}>
				<Text style={styles.label}>Tổng số lượng:</Text>
				<Text style={styles.value}>{totalQuantity}</Text>
			</View>
			<View style={styles.row}>
				<Text style={styles.label}>Tổng tiền:</Text>
				<Text style={styles.totalValue}>{currencyFormat(total)}</Text>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		backgroundColor: '#f8f9fa',
		padding: 15,
		borderRadius: 8,
		marginTop: 10,
		borderWidth: 1,
		borderColor: '#e9ecef',
	},
	row: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		marginBottom: 8,
	},
	label: {
		fontSize: 16,
		color: '#495057',
		fontWeight: '500',
	},
	value: {
		fontSize: 16,
		color: '#212529',
		fontWeight: '600',
	},
	totalValue: {
		fontSize: 18,
		color: '#28a745',
		fontWeight: 'bold',
	},
});
