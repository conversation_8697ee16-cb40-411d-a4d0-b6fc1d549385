import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { NumberInput } from '@/components';
import { currencyFormat } from '@/utils';
import { IMoneyDenomination } from '@/store';

interface MoneyInputRowProps {
	item: IMoneyDenomination;
	index: number;
	onChangeText: (index: number) => (value: string) => void;
}

export const MoneyInputRow: React.FC<MoneyInputRowProps> = ({
	item,
	index,
	onChangeText,
}) => {
	return (
		<View style={styles.row}>
			<View style={styles.label}>
				<Text style={styles.text}>{currencyFormat(item.name)}</Text>
			</View>
			<View style={styles.quantity}>
				<NumberInput
					value={item.quantity.toString() || '0'}
					onChangeText={onChangeText(index)}
					style={styles.input}
					keyboardType="numeric"
				/>
			</View>
			<View style={styles.total}>
				<Text style={styles.text}>{currencyFormat(item.total)}</Text>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	row: {
		flexDirection: 'row',
		alignItems: 'center',
		paddingVertical: 5,
	},
	label: {
		flex: 1,
		paddingHorizontal: 10,
	},
	quantity: {
		flex: 1,
		paddingHorizontal: 10,
	},
	total: {
		flex: 1,
		paddingHorizontal: 10,
	},
	text: {
		fontSize: 16,
		color: '#333',
	},
	input: {
		borderWidth: 1,
		borderColor: '#ddd',
		borderRadius: 4,
		paddingHorizontal: 10,
		paddingVertical: 8,
		fontSize: 16,
		backgroundColor: '#fff',
	},
});
