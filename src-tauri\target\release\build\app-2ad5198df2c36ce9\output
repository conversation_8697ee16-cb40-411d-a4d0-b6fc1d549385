cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rerun-if-changed=tauri.conf.json
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(dev)
cargo:rerun-if-changed=logo-ticket.png
cargo:rerun-if-changed=plugins\cashdrawer\cashdrawer.exe
cargo:rerun-if-changed=plugins\cashdrawer\cc3280.dll
cargo:rerun-if-changed=plugins\cashdrawer\inpout32.dll
cargo:rerun-if-changed=plugins\TwPrinter.exe
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=E:\touchcinema\touchcinema-pos\src-tauri\target\release\build\app-2ad5198df2c36ce9\out/resource.lib
cargo:rustc-link-search=native=E:\touchcinema\touchcinema-pos\src-tauri\target\release\build\app-2ad5198df2c36ce9\out
cargo:rustc-link-arg=/NODEFAULTLIB:libvcruntimed.lib
cargo:rustc-link-arg=/NODEFAULTLIB:vcruntime.lib
cargo:rustc-link-arg=/NODEFAULTLIB:vcruntimed.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libcmtd.lib
cargo:rustc-link-arg=/NODEFAULTLIB:msvcrt.lib
cargo:rustc-link-arg=/NODEFAULTLIB:msvcrtd.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libucrt.lib
cargo:rustc-link-arg=/NODEFAULTLIB:libucrtd.lib
cargo:rustc-link-arg=/DEFAULTLIB:libcmt.lib
cargo:rustc-link-arg=/DEFAULTLIB:libvcruntime.lib
cargo:rustc-link-arg=/DEFAULTLIB:ucrt.lib
