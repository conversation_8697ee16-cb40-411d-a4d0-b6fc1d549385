import { IBill } from '../types';

const parseBill = (order: IBill, printer: string) => {
	return `<Receipts>
  <Receipt name="${order.detail.hd}" printer="${printer}" left="10">
      <Text size="12" style="bold" align="center">TOUCH CINEMA</Text>
      <Text style="bold" align="center">212 <PERSON><PERSON><PERSON>, P.<PERSON>, TP.<PERSON>ik<PERSON>, Gia Lai</Text>
      <Text size="8" align="center">www.touchcinema.com</Text>
      <Space height="8"/>
      <Text size="15" style="bold" align="center">PHIẾU THANH TOÁN</Text>
      <Space height="10"/>
      <Table template="3,5">
          <Tr>
              <Td>
                  <Text style="bold" size="8">Quầy: ${
										order.detail.counter
									}</Text>
              </Td>
              <Td>
                  <Text style="bold" size="8">Ngày: ${order.detail.time}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold" size="8">NV: ${order.detail.seller}</Text>
              </Td>
              <Td>
                  <Text style="bold" size="8">Số HD: ${order.detail.hd}</Text>
              </Td>
          </Tr>
          <Tr>
              <Td>
                  <Text style="bold" size="8">${order.detail.member}</Text>
              </Td>
              <Td>
                  <Text style="bold" size="8">Mã GD: ${
										order.detail.order_id
									}</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="10"/>
      <Line type="dashed"/>
      <Space height="6"/>
      <Table template="9,2,3,4">
          <Tr>
              <Td>
                  <Text align="center" size="8">Hàng hoá</Text>
              </Td>
              <Td>
                  <Text align="center" size="8">SL</Text>
              </Td>
              <Td>
                  <Text align="center" size="8">Giá</Text>
              </Td>
              <Td>
                  <Text align="center" size="8">Thành tiền</Text>
              </Td>
          </Tr>
      </Table>
      <Space height="5"/>
      <Line type="dashed"/>
      <Space height="5"/>
      <Table template="9,2,3,4">
          ${order.products
						.map(
							(product) => `
          <Tr>
              <Td>
                  <Text align="center" size="8">${product[0]}</Text>
              </Td>
              <Td>
                  <Text align="center" size="8">${product[1]}</Text>
              </Td>
              <Td>
                  <Text align="center" size="8">${product[2]}</Text>
              </Td>
              <Td>
                  <Text align="center" size="8">${product[3]}</Text>
              </Td>
          </Tr>`,
						)
						.join('')}
      </Table>
      <Space height="5"/>
      <Line type="dashed"/>
      <Space height="8"/>
      <Table template="3,2">
            <Tr>
              <Td>
                  <Text style="bold">Tổng tiền thanh toán:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${order.payment.total}</Text>
              </Td>
            </Tr>
            <Tr>
              <Td>
                  <Text style="bold">Tiền khách trả:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${order.payment.input}</Text>
              </Td>
            </Tr>
            <Tr>
              <Td>
                  <Text style="bold">Tiền trả lại:</Text>
              </Td>
              <Td>
                  <Text style="bold" align="right">${
										order.payment.refund
									}</Text>
              </Td>
            </Tr>
        </Table>
        <Space height="15"/>
      <Line type="dashed"/>
      <Space height="10"/>
      <Text align="center">Cảm Ơn Quý Khách</Text>
      <Text align="center">Chúc Quý Khách Xem Phim Vui Vẻ</Text>
  </Receipt>
  </Receipts>`;
};
export default parseBill;
