import { createAsyncThunk } from '@reduxjs/toolkit';
import { get, post } from '@/services';
import { IReport, IReportShift } from '@/models';
import {
	setLoadingShifts,
	setLoadingReport,
	setError,
	setShifts,
	setReportData,
} from './Slice';

// Fetch shifts async thunk
export const fetchShifts = createAsyncThunk(
	'report/fetchShifts',
	async (_, { dispatch, rejectWithValue }) => {
		dispatch(setLoadingShifts(true));
		dispatch(setError(null));

		try {
			const response = await get('shifts');
			const shifts: IReportShift[] = response.data;

			dispatch(setShifts(shifts));
			dispatch(setLoadingShifts(false));
			return shifts;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Failed to fetch shifts';
			dispatch(setError(errorMessage));
			dispatch(setLoadingShifts(false));
			return rejectWithValue(errorMessage);
		}
	},
);

// Generate report async thunk
export const generateReport = createAsyncThunk(
	'report/generateReport',
	async (
		params: { from: string; to: string; shift: number },
		{ dispatch, rejectWithValue },
	) => {
		dispatch(setLoadingReport(true));
		dispatch(setError(null));

		try {
			const response = await post('report', params);

			const reportData: IReport = {
				detail: {
					start: response.data.from,
					end: response.data.to,
					user: response.data.fullname,
					amount: response.data.amount,
					shift: response.data.shift,
				},
				tickets: response.data.tickets,
				products: response.data['foods-drinks'],
				overview: response.data.products,
				categories: response.data.categories,
				total_categories: response.data.total_categories,
			};

			dispatch(setReportData(reportData));
			dispatch(setLoadingReport(false));
			return reportData;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Failed to generate report';
			dispatch(setError(errorMessage));
			dispatch(setLoadingReport(false));
			return rejectWithValue(errorMessage);
		}
	},
);
