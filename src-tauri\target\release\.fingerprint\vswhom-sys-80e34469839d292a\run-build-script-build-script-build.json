{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6817021698449653677, "build_script_build", false, 9996558279835142758]], "local": [{"RerunIfChanged": {"output": "release\\build\\vswhom-sys-80e34469839d292a\\output", "paths": ["build.rs", "ext/vswhom.cpp"]}}, {"RerunIfEnvChanged": {"var": "VCINSTALLDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "VisualStudioDir", "val": null}}, {"RerunIfEnvChanged": {"var": "VSCMD_ARG_VCVARS_SPECTRE", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSdkDir", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSDKVersion", "val": null}}, {"RerunIfEnvChanged": {"var": "LIB", "val": null}}, {"RerunIfEnvChanged": {"var": "PATH", "val": "C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_npx\\fdacfd67f4da830d\\node_modules\\.bin;E:\\touchcinema\\touchcinema-pos\\node_modules\\.bin;E:\\touchcinema\\node_modules\\.bin;E:\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Local\\nvm\\v22.15.0\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;E:\\touchcinema\\touchcinema-pos\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Local\\node\\corepack\\v1\\pnpm\\10.12.3\\dist\\node-gyp-bin;C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Git\\cmd;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;E:\\laragon\\bin;E:\\laragon\\bin\\apache\\httpd-2.4.54-win64-VS16\\bin;E:\\laragon\\bin\\composer;E:\\laragon\\bin\\git\\bin;E:\\laragon\\bin\\git\\cmd;E:\\laragon\\bin\\git\\mingw64\\bin;E:\\laragon\\bin\\git\\usr\\bin;E:\\laragon\\bin\\laragon\\utils;E:\\laragon\\bin\\mysql\\mysql-8.0.30-winx64\\bin;E:\\laragon\\bin\\nginx\\nginx-1.22.0;E:\\laragon\\bin\\ngrok;E:\\laragon\\bin\\nodejs\\node-v18;E:\\laragon\\bin\\notepad++;E:\\laragon\\bin\\php\\php-8.2.28-nts-Win32-vs16-x64;E:\\laragon\\bin\\python\\python-3.10;E:\\laragon\\bin\\python\\python-3.10\\Scripts;E:\\laragon\\bin\\redis\\redis-x64-********;E:\\laragon\\bin\\telnet;E:\\laragon\\usr\\bin;C:\\Users\\<USER>\\.cargo\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platform-tools;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;C:\\Users\\<USER>\\.rustup\\toolchains\\stable-x86_64-pc-windows-msvc\\bin"}}, {"RerunIfEnvChanged": {"var": "INCLUDE", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CXX", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB_x86_64_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CXXSTDLIB", "val": null}}, {"RerunIfEnvChanged": {"var": "CXXSTDLIB", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}