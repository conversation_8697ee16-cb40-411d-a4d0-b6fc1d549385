import React, { useMemo, useState } from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useSelector } from 'react-redux';

import { NumberInput, Button } from '@/components';
import { currencyFormat, getDateTime } from '@/utils';
import { getConfigs, getUser } from '@/store';
import { IMoney, printTask } from '@/modules/pos';

const defaultValue = [
	{
		name: 500000,
		quantity: 0,
		total: 0,
	},
	{
		name: 200000,
		quantity: 0,
		total: 0,
	},
	{
		name: 100000,
		quantity: 0,
		total: 0,
	},
	{
		name: 50000,
		quantity: 0,
		total: 0,
	},
	{
		name: 20000,
		quantity: 0,
		total: 0,
	},
	{
		name: 10000,
		quantity: 0,
		total: 0,
	},
	{
		name: 5000,
		quantity: 0,
		total: 0,
	},
	{
		name: 2000,
		quantity: 0,
		total: 0,
	},
	{
		name: 1000,
		quantity: 0,
		total: 0,
	},
	{
		name: 500,
		quantity: 0,
		total: 0,
	},
];

const MoneyScreen = () => {
	const configs = useSelector(getConfigs);
	const user = useSelector(getUser);
	const [data, setData] = useState(defaultValue);

	const onChangeText = (key: number) => (value: string) => {
		setData(
			data.map((item, index) =>
				index === key
					? {
							...item,
							quantity: parseInt(value || '0'),
							total: parseInt(value) > 0 ? parseInt(value) * item.name : 0,
						}
					: item,
			),
		);
	};

	const onPrint = async () => {
		const rawData: IMoney = {
			data: data.map((item) => [
				currencyFormat(item.name),
				item.quantity || 0,
				currencyFormat(item.total),
			]),
			total_money: currencyFormat(total),
			total_quantity: data.reduce(
				(total, item) => total + (item.quantity || 0),
				0,
			),
			counter: configs.counter,
			seller: user.name,
			time: getDateTime(),
		};
		await printTask(rawData, configs.printer, 'money');
	};

	const total = useMemo(() => {
		return data.reduce((tt, item) => tt + item.total, 0);
	}, [data]);

	return (
		<View style={styles.container}>
			<Text style={styles.title}>Kê tiền</Text>
			<View style={styles.inputWrap}>
				<View style={styles.row}>
					<View style={styles.label}>
						<Text style={styles.text}>Mệnh giá</Text>
					</View>
					<View style={styles.quantity}>
						<Text style={styles.text}>Số lượng</Text>
					</View>
					<View style={styles.total}>
						<Text style={styles.text}>Số tiền</Text>
					</View>
				</View>
				{data.map((item, index: number) => (
					<View style={styles.row} key={item.name}>
						<View style={styles.label}>
							<Text style={styles.text}>{currencyFormat(item.name)}</Text>
						</View>
						<View style={styles.quantity}>
							<NumberInput
								value={item.quantity.toString() || '0'}
								onChangeText={onChangeText(index)}
								style={styles.input}
								keyboardType="numeric"
							/>
						</View>
						<View style={styles.total}>
							<Text style={styles.text}>{currencyFormat(item.total)}</Text>
						</View>
					</View>
				))}
				<View style={styles.row}>
					<View style={styles.label}>
						<Text style={[styles.text, styles.bold]}>Tổng cộng</Text>
					</View>
					<View style={styles.quantity}></View>
					<View style={styles.total}>
						<Text style={[styles.text, styles.bold]}>
							{currencyFormat(total)}
						</Text>
					</View>
				</View>
			</View>
			<Button onPress={onPrint} text="In" style={styles.btn} />
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		padding: 10,
		alignItems: 'center',
	},
	title: {
		fontSize: 30,
		fontWeight: 'bold',
		paddingBottom: 10,
	},
	inputWrap: {
		flex: 1,
		width: '100%',
		maxWidth: 450,
		backgroundColor: '#fff',
		borderWidth: 1,
		borderColor: '#eee',
		padding: 15,
		borderRadius: 10,
	},
	input: {
		padding: 5,
		margin: 5,
		textAlign: 'center',
	},
	row: {
		width: '100%',
		flexDirection: 'row',
		borderBottomWidth: 1,
		borderColor: '#eee',
		alignItems: 'center',
	},
	label: {
		width: 100,
		alignItems: 'center',
		paddingVertical: 5,
	},
	quantity: {
		width: 100,
		textAlign: 'center',
	},
	total: {
		flex: 1,
		alignItems: 'center',
	},
	text: {
		fontSize: 16,
	},
	bold: {
		fontWeight: 'bold',
	},
	btn: {
		marginTop: 20,
		width: 120,
		alignItems: 'center',
		alignSelf: 'center',
	},
});

export default MoneyScreen;
