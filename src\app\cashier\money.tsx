import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { useSelector } from 'react-redux';

import {
	Button,
	MoneyInputRow,
	MoneyTableHeader,
	MoneyTotalSummary,
} from '@/components';
import { currencyFormat, getDateTime } from '@/utils';
import {
	getConfigs,
	getUser,
	getMoneyData,
	getMoneyTotal,
	getMoneyTotalQuantity,
	updateMoneyQuantity,
	useAppDispatch,
} from '@/store';
import { IMoney, printTask } from '@/modules/pos';

const MoneyScreen = () => {
	const dispatch = useAppDispatch();
	const configs = useSelector(getConfigs);
	const user = useSelector(getUser);
	const data = useSelector(getMoneyData);
	const total = useSelector(getMoneyTotal);
	const totalQuantity = useSelector(getMoneyTotalQuantity);

	const onChangeText = (key: number) => (value: string) => {
		const quantity = parseInt(value || '0');
		dispatch(updateMoneyQuantity({ index: key, quantity }));
	};

	const onPrint = async () => {
		const rawData: IMoney = {
			data: data.map((item) => [
				currencyFormat(item.name),
				item.quantity || 0,
				currencyFormat(item.total),
			]),
			total_money: currencyFormat(total),
			total_quantity: totalQuantity,
			counter: configs.counter,
			seller: user.name,
			time: getDateTime(),
		};
		await printTask(rawData, configs.printer, 'money');
	};

	return (
		<View style={styles.container}>
			<Text style={styles.title}>Kê tiền</Text>
			<View style={styles.inputWrap}>
				<MoneyTableHeader />
				{data.map((item, index: number) => (
					<MoneyInputRow
						key={item.name}
						item={item}
						index={index}
						onChangeText={onChangeText}
					/>
				))}
			</View>
			<MoneyTotalSummary total={total} totalQuantity={totalQuantity} />
			<Button onPress={onPrint} text="In" style={styles.btn} />
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		padding: 20,
		alignItems: 'center',
		flex: 1,
	},
	title: {
		fontSize: 30,
		fontWeight: 'bold',
		paddingBottom: 20,
		color: '#333',
	},
	inputWrap: {
		flex: 1,
		width: '100%',
		maxWidth: 600,
		backgroundColor: '#fff',
		borderWidth: 1,
		borderColor: '#e9ecef',
		borderRadius: 10,
		overflow: 'hidden',
		marginBottom: 20,
	},
	btn: {
		marginTop: 20,
		width: 120,
		alignItems: 'center',
		alignSelf: 'center',
	},
});

export default MoneyScreen;
