{"name": "touchcinema-pos", "main": "index.js", "version": "2.2.2", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "desktop:dev": "tauri dev", "desktop:build": "npx @dotenvx/dotenvx run -- tauri build", "desktop:update": "tw-updater update -p tauri", "test": "jest --watchAll", "lint": "expo lint", "prepare": "husky install"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-picker/picker": "2.11.1", "@reduxjs/toolkit": "^2.8.2", "@tauri-apps/api": "^1.6", "axios": "^1.10.0", "expo": "~53.0.13", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-image": "~2.3.0", "expo-linking": "~7.1.5", "expo-router": "~5.1.1", "js-htmlencode": "^0.3.0", "react": "19.0.0", "react-dom": "19.0.0", "react-indiana-drag-scroll": "^2.2.1", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-mask-text": "^0.14.2", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-tab-view": "^4.0.5", "react-native-toast-message": "^2.3.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "react-redux": "^9.2.0", "socket.io-client": "2", "use-debounce": "^10.0.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@tauri-apps/cli": "^1.6", "@types/jest": "^29.5.12", "@types/react": "~19.0.14", "@types/react-test-renderer": "^18.3.0", "eslint": "^9.29.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^8.0.0", "jest": "^29.2.1", "jest-expo": "~53.0.7", "lint-staged": "^15.5.2", "prettier": "^3.4.2", "react-test-renderer": "18.3.1", "typescript": "^5.8.3"}, "lint-staged": {"*.{js, jsx,ts,tsx}": ["eslint --quiet --fix"], "*.{json,js,ts,jsx,tsx,html}": ["prettier --write --ignore-unknown"]}, "private": true, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["@tauri-apps/api", "js-htmlencode", "react-indiana-drag-scroll", "socket.io-client", "use-debounce"]}}}, "packageManager": "pnpm@10.12.3+sha512.467df2c586056165580ad6dfb54ceaad94c5a30f80893ebdec5a44c5aa73c205ae4a5bb9d5ed6bb84ea7c249ece786642bbb49d06a307df218d03da41c317417"}